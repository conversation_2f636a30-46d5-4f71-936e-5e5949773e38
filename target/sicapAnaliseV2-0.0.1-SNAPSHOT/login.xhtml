<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:h="http://xmlns.jcp.org/jsf/html"
  xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
  xmlns:f="http://xmlns.jcp.org/jsf/core"
  xmlns:p="http://primefaces.org/ui"
>
  <h:head>
    <f:facet name="first">
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta
        http-equiv="Content-Type"
        content="text/html; accept-charset=UTF-8"
      />
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      />
      <meta name="apple-mobile-web-app-capable" content="yes" />
    </f:facet>

    <h:outputScript library="sentinel-layout" name="js/layout.js" />
    <h:outputScript name="locale-primefaces.js" library="js" />
    <h:outputScript name="css_browser_selector.js" library="js" />

    <h:outputStylesheet
      library="sentinel-layout"
      name="css/font-icon-layout.css"
    />
    <h:outputStylesheet
      library="sentinel-layout"
      name="css/sentinel-layout.css"
    />
    <h:outputStylesheet library="sentinel-layout" name="css/core-layout.css" />

    <h:outputStylesheet library="css" name="index.css" />

    <title>TCE-AC | SICAP Análise</title>

    <link
      rel="icon"
      type="image/png"
      href="#{request.contextPath}/resources/imagens/logo_sicap.png"
    />
  </h:head>

  <h:body>
    <style>
      #login-box {
        min-height: auto;
      }

      .ui-fluid .ui-inputtext {
        width: 85%;
      }

      .ui-fluid .ui-password {
        width: 85%;
      }

      .button {
        background-color: #008cba;
        border: none;
        color: white;
        padding: 10px 28px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 16px;
        margin: 4px 2px;
        cursor: pointer;
        width: 85%;
      }
    </style>
    <div id="layout-header" class="Unselectable fontRegular">
      <h:outputLink
        id="tceLink"
        value="http://www.tce.ac.gov.br"
        title="Tribunal de Contas do Estado do Acre"
      >
        <h:graphicImage
          id="iconTce"
          library="imagens"
          name="icon_tce.png"
          styleClass="ShowOnDesktop"
        />
        <h:graphicImage
          id="iconTceMobile"
          library="imagens"
          name="icon_tce3.png"
          styleClass="ShowOnMobile"
        />
      </h:outputLink>

      <h:outputLink id="linkHome" title="Página inicial do Sistema" action="#">
        <h:graphicImage
          id="iconSistema"
          library="imagens"
          name="logo_sicap.png"
        />
        <h:outputText id="lblHeader" value="SICAP Análise" />
      </h:outputLink>
    </div>

    <div id="layout-portlets-cover" class="fontRegular">
      <div id="corpo" class="Container100 BorBotLeaden ui-fluid">
        <div
          class="Container80 MaxWid500 Fnone MarAuto TexAlCenter PaddingTopPercent10"
        >
          <a id="login-logo">
            <h:graphicImage
              library="imagens"
              name="logo_sicap.png"
              styleClass="Fleft"
            />
            <span class="Fs30 FontTitilliumSemiBoldItalic Fleft"
              ><span class="softblue"> SICAP</span>
              <span class="hardblue"> Análise</span></span
            >
          </a>
        </div>

        <h:form id="frmLogin" prependId="false" acceptcharset="UTF-8">
          <p:growl
            id="mensagem"
            showDetail="false"
            sticky="false"
            widgetVar="growl"
            life="6000"
            autoUpdate="true"
          />

          <!-- Dialog de status para mostrar durante o login -->
          <p:dialog
            id="statusDialog"
            widgetVar="statusDialog"
            modal="true"
            closable="false"
            resizable="false"
            width="300"
            height="100"
            header="Aguarde..."
          >
            <div style="text-align: center; padding: 20px">
              <i class="fa fa-spinner fa-spin fa-2x"></i>
              <br /><br />
              Efetuando login...
            </div>
          </p:dialog>
          <div
            class="Container80 MaxWid500 white-back Fnone MarAuto BordRad10"
            id="login-box"
          >
            <div class="Container100">
              <div
                class="Container100 TexAlCenter Animated05 BoxSizeBorder left TabBtn TabBtnActiveLeft"
              >
                <div class="Container90 Fnone MarAuto TexAlCenter" id="TAB1">
                  <div class="EmptyBox30"></div>
                  <p:focus />
                  <p:inputText
                    value="#{loginBean.login}"
                    required="true"
                    placeholder="Usuário"
                    styleClass="Container90 Fnone TexAlCenter MarAuto Fs18"
                    requiredMessage="O campo login deve ser preenchido."
                  />

                  <div class="EmptyBox30" />

                  <p:password
                    value="#{loginBean.senha}"
                    required="true"
                    placeholder="Senha"
                    styleClass="Container90 Fnone TexAlCenter MarAuto Fs18"
                    requiredMessage="O campo senha deve ser preenchido."
                    feedback="false"
                  />

                  <div class="EmptyBox30" />

                  <p:commandButton
                    ajax="false"
                    styleClass="Container90 Fnone TexAlCenter MarAuto Fs16"
                    update="frmLogin"
                    value="Entrar"
                    action="#{loginBean.efetuarLogin}"
                    onclick="PF('statusDialog').show()"
                    oncomplete="PF('statusDialog').hide()"
                  />

                  <div class="EmptyBox30" />
                </div>
              </div>
            </div>
          </div>
        </h:form>
      </div>

      <div id="footer" class="Container100" style="margin-bottom: 1px">
        <div class="ContainerIndent">
          <div class="Container100">
            <a
              href="http://tceac.tc.br"
              target="_blank"
              class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft"
              >SITE DO TCE</a
            >
            <span class="Fleft gray Fs12" style="padding: 0px 5px">|</span>
            <a
              href="http://sistemas.tceac.tc.br/portaldogestor"
              target="_blank"
              class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft"
              >PORTAL DO GESTOR</a
            >
            <span class="Fleft gray Fs12" style="padding: 0px 5px">|</span>
            <a
              href="http://sistemas.tceac.tc.br/cidadao"
              target="_blank"
              class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft"
              >PORTAL DO CIDADÃO</a
            >
            <span class="Fleft gray Fs12" style="padding: 0px 5px">|</span>
            <a
              href="http://sistemas.tceac.tc.br/portaldaslicitacoes"
              target="_blank"
              class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft"
              >PORTAL DAS LICITAÇÕES</a
            >
            <span class="Fleft gray Fs12" style="padding: 0px 5px">|</span>
            <a
              href="http://sistemas.tceac.tc.br/transparencia"
              target="_blank"
              class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft"
              >TRANSPARÊNCIA</a
            >

            <div class="EmptyBox10 ShowOnMobile"></div>
            <span
              class="DispInlBlock FontSourceSansLight gray Fs12 Fright TexAlRight FloatNoneOnMobile"
            >
              TCE-AC | Todos os direitos reservados.</span
            >
          </div>

          <!-- 			<div class="EmptyBox10"></div> -->

          <div class="Container50 Responsive100">
            <div class="EmptyBox10"></div>
            <span class="DispBlock Wid100 FontSourceSansLight gray Fs12"
              >Tribunal de Contas do Estado do Acre <br /> Av. Ceará, 2994, 7º
              BEC - Rio Branco-Acre - CEP 69.918-111 <br /> Suporte:
              <EMAIL> | Tel.:(68) 3025-2009 / 2069
            </span>
          </div>
          <div class="Container50 Responsive100">
            <div class="DispInlBlock Fright">
              <a
                href="https://www.facebook.com/tceac"
                target="_blank"
                class="hardblue Fs30"
                ><i class="fa fa-facebook-square" title="Facebook"></i
              ></a>
              <a
                href="https://plus.google.com/105996813613508600853"
                target="_blank"
                class="hardblue Fs30"
                ><i class="fa fa-google-plus-square" title="Google+"></i
              ></a>
              <a
                href="https://www.youtube.com/channel/UCoNe1qAoUfR6FuQ1yOsbxjQ"
                target="_blank"
                class="hardblue Fs30"
                ><i class="fa fa-youtube-square" title="YouTube"></i
              ></a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script type="text/javascript">
      $(document).ready(function () {
        ajustaHeights();
        $(window)
          .on("resize", function () {
            ajustaHeights();
          })
          .resize();
      });

      $(document).ajaxSuccess(function () {
        ajustaHeights();
      });

      ajustaHeights = function () {
        $("#corpo").css("min-height", 100);
        $("#corpo").css(
          "min-height",
          $(document).height() -
            $("#layout-header").height() -
            $("#footer").height() -
            10
        );
      };
    </script>
  </h:body>
</html>
