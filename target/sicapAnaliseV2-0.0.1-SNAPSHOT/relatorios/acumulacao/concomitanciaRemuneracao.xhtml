<ui:composition
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:h="http://java.sun.com/jsf/html"
  xmlns:f="http://java.sun.com/jsf/core"
  xmlns:ui="http://java.sun.com/jsf/facelets"
  xmlns:p="http://primefaces.org/ui"
>
  <ui:define name="content">
    <h:form id="frmConcomitanciaRemuneracao" prependId="false">
      <p:fieldset legend="Relatório de Concomitância de Remuneração por CPF">
        <p:dataTable
          id="tblConcomitancia"
          var="concomitancia"
          value="#{concomitanciaRemuneracaoBean.listaConcomitancias}"
          paginator="true"
          rows="20"
          rowKey="#{concomitancia.cpf}"
        >
          <p:column headerText="CPF" styleClass="TexAlCenter" width="100">
            <h:outputText value="#{concomitancia.cpf}" />
          </p:column>
          <p:column headerText="Nome do Servidor" width="200">
            <h:outputText value="#{concomitancia.nomeServidor}" />
          </p:column>
          <p:column headerText="Período de Concomitância" width="150">
            <h:outputText value="#{concomitancia.periodoConcomitancia}" />
          </p:column>
          <p:column headerText="Cargos Acumulados" width="120">
            <h:outputText value="#{concomitancia.cargosAcumulados}" />
          </p:column>
          <p:column headerText="Nº Meses Simultâneos" width="100">
            <h:outputText value="#{concomitancia.numMesesSimultaneos}" />
          </p:column>
          <p:column
            headerText="Total Recebido Concomitante"
            width="130"
            styleClass="TexAlRight"
          >
            <h:outputText value="#{concomitancia.totalRecebidoConcomitante}">
              <f:convertNumber
                type="currency"
                currencySymbol="R$"
                locale="pt_BR"
              />
            </h:outputText>
          </p:column>
        </p:dataTable>
      </p:fieldset>
    </h:form>
  </ui:define>
</ui:composition>
