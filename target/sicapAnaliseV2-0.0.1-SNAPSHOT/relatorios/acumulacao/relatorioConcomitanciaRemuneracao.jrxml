<?xml version="1.0" encoding="UTF-8"?>
<!-- Criado com base em relatorioAcumulacao.jrxml -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
    name="relatorioConcomitanciaRemuneracao" pageWidth="842" pageHeight="595" orientation="Landscape"
    columnWidth="800" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true">
    <parameter name="REPORT_TITLE" class="java.lang.String"/>
    <field name="cpf" class="java.lang.String"/>
    <field name="nomeServidor" class="java.lang.String"/>
    <field name="periodoConcomitancia" class="java.lang.String"/>
    <field name="cargosAcumulados" class="java.lang.String"/>
    <field name="numMesesSimultaneos" class="java.lang.Integer"/>
    <field name="totalRecebidoConcomitante" class="java.math.BigDecimal"/>
    <title>
        <band height="40">
            <staticText>
                <reportElement x="0" y="0" width="800" height="40"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="18" isBold="true"/>
                </textElement>
                <text><![CDATA[Relatório de Concomitância de Remuneração por CPF]]></text>
            </staticText>
        </band>
    </title>
    <columnHeader>
        <band height="30">
            <staticText><reportElement x="0" y="0" width="100" height="30"/><textElement textAlignment="Center"/><text><![CDATA[CPF]]></text></staticText>
            <staticText><reportElement x="100" y="0" width="200" height="30"/><textElement textAlignment="Center"/><text><![CDATA[Nome do Servidor]]></text></staticText>
            <staticText><reportElement x="300" y="0" width="150" height="30"/><textElement textAlignment="Center"/><text><![CDATA[Período de Concomitância]]></text></staticText>
            <staticText><reportElement x="450" y="0" width="120" height="30"/><textElement textAlignment="Center"/><text><![CDATA[Cargos Acumulados]]></text></staticText>
            <staticText><reportElement x="570" y="0" width="100" height="30"/><textElement textAlignment="Center"/><text><![CDATA[Nº Meses Simultâneos]]></text></staticText>
            <staticText><reportElement x="670" y="0" width="130" height="30"/><textElement textAlignment="Center"/><text><![CDATA[Total Recebido Concomitante]]></text></staticText>
        </band>
    </columnHeader>
    <detail>
        <band height="20">
            <textField><reportElement x="0" y="0" width="100" height="20"/><textElement/><textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression></textField>
            <textField><reportElement x="100" y="0" width="200" height="20"/><textElement/><textFieldExpression><![CDATA[$F{nomeServidor}]]></textFieldExpression></textField>
            <textField><reportElement x="300" y="0" width="150" height="20"/><textElement/><textFieldExpression><![CDATA[$F{periodoConcomitancia}]]></textFieldExpression></textField>
            <textField><reportElement x="450" y="0" width="120" height="20"/><textElement/><textFieldExpression><![CDATA[$F{cargosAcumulados}]]></textFieldExpression></textField>
            <textField><reportElement x="570" y="0" width="100" height="20"/><textElement textAlignment="Center"/><textFieldExpression><![CDATA[$F{numMesesSimultaneos}]]></textFieldExpression></textField>
            <textField pattern="#,##0.00"><reportElement x="670" y="0" width="130" height="20"/><textElement textAlignment="Right"/><textFieldExpression><![CDATA[$F{totalRecebidoConcomitante}]]></textFieldExpression></textField>
        </band>
    </detail>
</jasperReport>
